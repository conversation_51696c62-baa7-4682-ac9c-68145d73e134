package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.changlian.ChangLianCreateOrderResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.changlian.ChangLianLabelResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.math.BigInteger
import java.security.MessageDigest

@Service
class ChangLianPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val waybillRepository: WaybillRepository,
    @Value("\${chang-lian.apikey}") private val apikey: String,
    @Value("\${chang-lian.apiSecret}") private val apiSecret: String,
    @Value("\${chang-lian.usertoken}") private val usertoken: String,
    @Value("\${chang-lian.api.baseUrl}") private val baseUrl: String,
) : OrderPredictionRemoteService {

    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "常联 创建运单失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }

        try {
            val requestBody = buildCreateOrderRequest(waybill, orderList)
            log.info { "常联 创建运单请求 | 订单号: ${waybill.orderNo} | 请求参数: $requestBody" }

            val response = sendRequest("createOrder", requestBody)
            log.info { "常联 创建运单响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }

            handleCreateOrderResponse(response, waybill)
        } catch (e: Exception) {
            log.error(e) { "常联 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        // 常联API文档中没有提供取消订单的接口，返回true表示不支持取消
        log.info { "常联 不支持取消预报 | 订单号: ${waybill.orderNo}" }
        return true
    }

    override fun getPrintUrl(waybill: Waybill): String {
        if (waybill.waybillNo.isNullOrBlank()) {
            log.error { "常联 获取面单失败 | 订单号: ${waybill.orderNo} | 原因: 运单号为空" }
            return ""
        }

        try {
            val requestBody = JSON.toJSONString(
                mapOf(
                    "mergePdf" to 1,
                    "nos" to listOf(waybill.waybillNo)
                )
            )

            val response = sendRequest("label", requestBody)
            log.info { "常联 获取面单响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }

            val labelResponse: ChangLianLabelResponse = JSON.parseObject(response)
            if (labelResponse.code == 0 && !labelResponse.labelUrl.isNullOrBlank()) {
                return uploadWaybill(waybill, labelResponse.labelUrl)
            } else {
                log.error { "常联 获取面单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${labelResponse.msg}" }
                return ""
            }
        } catch (e: Exception) {
            log.error(e) { "常联 获取面单异常 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            return ""
        }
    }

    private fun buildCreateOrderRequest(waybill: Waybill, orderList: List<SubOrder>): String {
        val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
        val totalWeight = waybillRequests.sumOf { it.weight }

        // 构建申报明细
        val orderDeclareList = waybillRequests.map { order ->
            mapOf(
                "englishName" to order.name,
                "chineseName" to order.cnName,
                "quantity" to order.qty,
//                "brand" to (order.supplierName.takeIf { it.isNotBlank() } ?: "无"),
                "unitNetWeightD" to order.weight.toDouble(),
                "unitDeclarePriceD" to order.price(waybillRequests.sumOf { it.qty }, order.country).toDouble(),
                "hsCode" to (order.hsCode ?: ""),
                "material" to (order.material ?: ""),
                "sku" to order.skuCode()
            )
        }

        // 构建包裹明细
        val packageList = listOf(
            mapOf(
                "packageWeightD" to totalWeight.toDouble()
            )
        )

        return JSON.toJSONString(
            mapOf(
                "customerOrderNo" to waybill.orderNo,
                "logisticsProductCode" to waybill.shipping.shipMethod,
                "taxPayMode" to 11, // DDP
                "parcelType" to 10, // 包裹
                "declareCurrency" to "USD",
                "customerRemark" to  waybillRequests.joinToString(";") { it.orderNo + "-" + it.skuCode() },
                "shipper" to mapOf(
                    "shipperName" to "DAMU",
                    "shipperCompany" to "香港芮塔",
                    "shipperPhone" to "15356637391",
                    "shipperEmail" to "<EMAIL>",
                    "shipperCountry" to "CN",
                    "shipperProvince" to "浙江",
                    "shipperCity" to "杭州",
                    "shipperAddress" to "九和中心",
                    "shipperPostcode" to "310020"
                ),
                "consignee" to mapOf(
                    "consigneeName" to waybill.recipient.receiverName,
                    "consigneePhone" to waybill.recipient.phone,
                    "consigneeCountry" to waybill.recipient.country,
                    "consigneeProvince" to waybill.recipient.state,
                    "consigneeCity" to waybill.recipient.city,
                    "consigneeAddress" to waybill.recipient.address(),
                    "consigneePostcode" to waybill.recipient.postcode,
                    "consigneeTaxNo" to waybill.taxNumber
                ),
                "orderDeclareList" to orderDeclareList,
                "packageList" to packageList
            )
        )
    }

    private fun sendRequest(method: String, requestBody: String): String {
        val timestamp = (System.currentTimeMillis() / 1000).toString()
        val signature = generateSignature(requestBody, timestamp)

        val request = Request.Builder()
            .url("$baseUrl/$method")
            .header("Content-Type", "application/json;charset=UTF-8")
            .header("apikey", apikey)
            .header("signature", signature)
            .header("timestamp", timestamp)
            .header("usertoken", usertoken)
            .post(requestBody.toRequestBody())
            .build()

        return okHttpClient.newCall(request).execute().use { response ->
            response.body?.string() ?: throw Exception("Empty response body")
        }
    }

    private fun generateSignature(requestBody: String, timestamp: String): String {
        val signString = "$apikey$apiSecret$usertoken$timestamp$requestBody"
        return getMD5(signString)
    }

    private fun getMD5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val messageDigest = md.digest(input.toByteArray())
        val number = BigInteger(1, messageDigest)
        var hashText = number.toString(16)
        while (hashText.length < 32) {
            hashText = "0$hashText"
        }
        return hashText.lowercase()
    }

    private fun handleCreateOrderResponse(responseBody: String, waybill: Waybill) {
        val response: ChangLianCreateOrderResponse = JSON.parseObject(responseBody)

        if (response.code == "0") {
            val data = response.data
            if (data != null) {
                waybill.status = WayBillStatus.PENDING
                waybill.waybillNo = data.waybillNo
                log.info { "常联 创建运单成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }

                waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
                waybill.status = WayBillStatus.COMPLETED
                eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
                waybill.clearErrorMsg()
            } else {
                log.error { "常联 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: 响应数据为空" }
                waybill.failed("Response data is null")
            }
        } else {
            log.error { "常联 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${response.msg}" }
            waybill.failed(response.msg)
        }
        waybillRepository.saveAndFlush(waybill)
    }

    /**
     * 测试方法 - 用于验证常联API连接
     */
    fun testConnection(): String {
        try {
            val testRequestBody = JSON.toJSONString(
                mapOf(
                    "customerOrderNo" to "TEST_${System.currentTimeMillis()}",
                    "logisticsProductCode" to "TEST_PRODUCT"
                )
            )

            log.info { "常联 测试连接 | 请求参数: $testRequestBody" }
            val response = sendRequest("createOrder", testRequestBody)
            log.info { "常联 测试连接响应: $response" }
            return response
        } catch (e: Exception) {
            log.error(e) { "常联 测试连接失败: ${e.message}" }
            return "测试失败: ${e.message}"
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
